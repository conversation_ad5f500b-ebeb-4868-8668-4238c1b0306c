import 'dart:async';

import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'my_site.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Supabase.initialize(
    url: 'https://otwuljlssoomnqixfywb.supabase.co',
    anonKey:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im90d3Vsamxzc29vbW5xaXhmeXdiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NjcyMTYsImV4cCI6MjA2NTQ0MzIxNn0.rzbY8s4u0EgwNCn-WdoZk2xSwKQX_0nuJ2clzw2fWoU',
    authOptions: FlutterAuthClientOptions(
      autoRefreshToken: true, // ✅ refreshes expired tokens autom
      detectSessionInUri: true, // ✅ detects session in callback URLs
    ),
  );

  runZonedGuarded(() {
    runApp(const MySite());
  }, (error, stackTrace) {
    print("Uncaught error: $error");
  });

}
