import 'package:flutter/material.dart';

import '../../../core/res/responsive.dart';
import '../projects/projects.dart';
import 'portfolio_desktop.dart';
import 'portfolio_mobile.dart';

class Portfolio extends StatelessWidget {
  const Portfolio({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Responsive(
      mobile: PortfolioMobileTab(),
      tablet: PortfolioMobileTab(),
      desktop: PortfolioDesktop(),
    );
  }
}

class ProjectViewV2 extends StatefulWidget {
  const ProjectViewV2({super.key});

  @override
  State<ProjectViewV2> createState() => _ProjectViewV2State();
}

class _ProjectViewV2State extends State<ProjectViewV2> {
  @override
  Widget build(BuildContext context) {
    return const Responsive(
      mobile: ProjectsView(),
      tablet: ProjectsView(),
      desktop: ProjectsView(),
    );
  }
}
